/**
 * Patient storage utilities for DermaCare bi-directional sync service
 *
 * This module provides advanced patient storage operations that replicate the
 * functionality of v3Integration's Contact.searchCreateOrUpdate() method.
 * It implements smart patient search and upsert logic with fallback matching
 * to ensure complete feature parity with the legacy system.
 *
 * **Key Features:**
 * - Smart patient search with priority-based matching (apId → ccId → email → phone)
 * - Upsert operations that create or update existing records
 * - Maintains data integrity across both CC and AP platforms
 * - Optimized database queries with proper indexing
 * - Comprehensive error handling and logging
 *
 * **Search Priority Logic:**
 * 1. Search by AP ID (highest priority)
 * 2. Search by CC ID
 * 3. Search by email address
 * 4. Search by phone number (lowest priority)
 *
 * **Data Integrity:**
 * - Preserves existing platform-specific data when updating
 * - <PERSON>les null/undefined values gracefully
 * - Maintains proper timestamps for sync coordination
 * - Validates required fields before operations
 *
 * @example
 * ```typescript
 * // Search and update patient with CC data
 * const patient = await searchCreateOrUpdatePatient({
 *   ccId: 12345,
 *   ccData: ccPatientData,
 *   email: "<EMAIL>",
 *   phone: "+**********"
 * });
 *
 * // Search and update patient with AP data
 * const patient = await searchCreateOrUpdatePatient({
 *   apId: "ap_contact_123",
 *   apData: apContactData,
 *   email: "<EMAIL>"
 * });
 * ```
 *
 * @since 1.0.0
 * @version 1.0.0
 */

import { getDb } from "@database";
import { patient } from "@database/schema";
import type {
	GetAPContactType,
	GetCCPatientType,
} from "@type";
import { logError } from "@utils/errorLogger";
import { and, eq, or } from "drizzle-orm";

/**
 * Cleans email and phone values like v3Integration Contact model
 * Converts empty strings to null, matching the v3Integration behavior
 *
 * @param value - Email or phone value to clean
 * @returns Cleaned value (null if empty/whitespace)
 */
function cleanContactValue(value: string | null | undefined): string | null {
	if (!value || value.trim() === '') {
		return null;
	}
	return value.trim();
}

/**
 * Patient search and upsert payload interface
 *
 * Defines the structure for patient search and upsert operations.
 * Supports data from both CC and AP platforms with flexible matching criteria.
 */
export interface PatientUpsertPayload {
	/** AutoPatient contact ID for direct AP matching */
	apId?: string;
	/** CliniCore patient ID for direct CC matching */
	ccId?: number;
	/** Email address for fallback matching */
	email?: string;
	/** Phone number for fallback matching */
	phone?: string;
	/** Complete CC patient data for storage */
	ccData?: GetCCPatientType;
	/** Complete AP contact data for storage */
	apData?: GetAPContactType;
	/** Source platform identifier */
	source?: "ap" | "cc";
}

/**
 * Searches for existing patient or creates new one with smart matching logic
 *
 * This function replicates the core functionality of v3Integration's
 * Contact.searchCreateOrUpdate() method. It implements a priority-based
 * search strategy to find existing patients and either updates them with
 * new data or creates a new patient record if none is found.
 *
 * **Search Strategy:**
 * 1. **Direct ID Match**: First attempts to find patient by platform-specific ID
 *    - If apId provided: searches by patient.apId
 *    - If ccId provided: searches by patient.ccId
 * 2. **Fallback Matching**: If no direct ID match, searches by contact information
 *    - Searches by email address
 *    - Searches by phone number
 *    - Uses OR logic to find any matching contact info
 *
 * **Update Logic:**
 * - Preserves existing data while updating with new information
 * - Only updates fields that are provided in the payload
 * - Maintains proper timestamps for sync coordination
 * - Updates both platform-specific data and common fields
 *
 * **Create Logic:**
 * - Creates new patient record if no existing patient found
 * - Populates all provided fields
 * - Sets appropriate timestamps
 * - Generates UUID for internal patient ID
 *
 * @param payload - Patient data and search criteria
 * @param payload.apId - AP contact ID for direct matching
 * @param payload.ccId - CC patient ID for direct matching
 * @param payload.email - Email for fallback matching
 * @param payload.phone - Phone for fallback matching
 * @param payload.ccData - Complete CC patient data
 * @param payload.apData - Complete AP contact data
 * @param payload.source - Source platform ("ap" or "cc")
 *
 * @returns Promise resolving to patient record (existing or newly created)
 *
 * @throws {Error} When database operations fail
 * @throws {Error} When no search criteria provided
 *
 * @example
 * ```typescript
 * // Search by CC ID and update with new data
 * const patient = await searchCreateOrUpdatePatient({
 *   ccId: 12345,
 *   ccData: updatedCCData,
 *   email: "<EMAIL>",
 *   source: "cc"
 * });
 *
 * // Search by email and create if not found
 * const patient = await searchCreateOrUpdatePatient({
 *   email: "<EMAIL>",
 *   phone: "+**********",
 *   apData: apContactData,
 *   source: "ap"
 * });
 *
 * // Search with fallback logic
 * const patient = await searchCreateOrUpdatePatient({
 *   apId: "ap_123", // Try AP ID first
 *   email: "<EMAIL>", // Fallback to email
 *   ccData: ccPatientData
 * });
 * ```
 */
export async function searchCreateOrUpdatePatient(
	payload: PatientUpsertPayload,
): Promise<typeof patient.$inferSelect> {
	try {
		const db = getDb();
		let existingPatient: typeof patient.$inferSelect | undefined;

		// Priority 1: Search by platform-specific IDs
		if (payload.apId) {
			console.log(`Searching patient by AP ID: ${payload.apId}`);
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.apId, payload.apId))
				.limit(1);
			existingPatient = results[0];
		} else if (payload.ccId) {
			console.log(`Searching patient by CC ID: ${payload.ccId}`);
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.ccId, payload.ccId))
				.limit(1);
			existingPatient = results[0];
		}

		// Priority 2: Fallback search by email and phone
		if (!existingPatient && (payload.email || payload.phone)) {
			console.log(`Fallback search by email: ${payload.email}, phone: ${payload.phone}`);
			
			const conditions = [];
			if (payload.email) {
				conditions.push(eq(patient.email, payload.email));
			}
			if (payload.phone) {
				conditions.push(eq(patient.phone, payload.phone));
			}

			if (conditions.length > 0) {
				const results = await db
					.select()
					.from(patient)
					.where(or(...conditions))
					.limit(1);
				existingPatient = results[0];
			}
		}

		// Update existing patient or create new one
		if (existingPatient) {
			console.log(`Updating existing patient: ${existingPatient.id}`);
			
			// Prepare update data - only update fields that are provided
			const updateData: Partial<typeof patient.$inferInsert> = {
				updatedAt: new Date(),
			};

			// Update platform-specific IDs
			if (payload.apId) updateData.apId = payload.apId;
			if (payload.ccId) updateData.ccId = payload.ccId;

			// Update contact information with v3Integration-style cleaning
			if (payload.email !== undefined) updateData.email = cleanContactValue(payload.email);
			if (payload.phone !== undefined) updateData.phone = cleanContactValue(payload.phone);

			// Update platform-specific data and timestamps
			if (payload.ccData) {
				updateData.ccData = payload.ccData;
				updateData.ccUpdatedAt = new Date(payload.ccData.updatedAt || new Date());
			}
			if (payload.apData) {
				updateData.apData = payload.apData;
				updateData.apUpdatedAt = new Date(
					payload.apData.dateUpdated || payload.apData.dateAdded || new Date()
				);
			}

			const [updatedPatient] = await db
				.update(patient)
				.set(updateData)
				.where(eq(patient.id, existingPatient.id))
				.returning();

			console.log(`Patient updated successfully: ${updatedPatient.id}`);
			return updatedPatient;
		} else {
			console.log(`Creating new patient`);
			
			// Validate that we have enough data to create a patient (using cleaned values)
			const cleanedEmail = cleanContactValue(payload.email);
			const cleanedPhone = cleanContactValue(payload.phone);
			if (!cleanedEmail && !cleanedPhone) {
				throw new Error("Cannot create patient: email or phone is required");
			}

			// Prepare insert data with v3Integration-style cleaning
			const insertData: typeof patient.$inferInsert = {
				apId: payload.apId || null,
				ccId: payload.ccId || null,
				email: cleanContactValue(payload.email),
				phone: cleanContactValue(payload.phone),
				ccData: payload.ccData || null,
				apData: payload.apData || null,
			};

			// Set timestamps based on platform data
			if (payload.ccData) {
				insertData.ccUpdatedAt = new Date(payload.ccData.updatedAt || new Date());
			}
			if (payload.apData) {
				insertData.apUpdatedAt = new Date(
					payload.apData.dateUpdated || payload.apData.dateAdded || new Date()
				);
			}

			const [newPatient] = await db
				.insert(patient)
				.values(insertData)
				.returning();

			console.log(`New patient created successfully: ${newPatient.id}`);
			return newPatient;
		}
	} catch (error) {
		await logError(
			"PATIENT_SEARCH_CREATE_UPDATE_ERROR",
			error,
			{
				payload: {
					apId: payload.apId,
					ccId: payload.ccId,
					email: payload.email,
					phone: payload.phone,
					source: payload.source,
				},
			},
			"PatientStorage",
		);
		throw error;
	}
}

/**
 * Finds patient by multiple criteria with priority-based search
 *
 * This is a read-only version of searchCreateOrUpdatePatient that only
 * searches for existing patients without creating or updating records.
 * Useful for lookup operations where you don't want to modify data.
 *
 * @param criteria - Search criteria with same priority logic
 * @returns Promise resolving to patient record or null if not found
 */
export async function findPatientByMultipleCriteria(
	criteria: Pick<PatientUpsertPayload, "apId" | "ccId" | "email" | "phone">,
): Promise<typeof patient.$inferSelect | null> {
	try {
		const db = getDb();

		// Priority 1: Search by platform-specific IDs
		if (criteria.apId) {
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.apId, criteria.apId))
				.limit(1);
			return results[0] || null;
		}

		if (criteria.ccId) {
			const results = await db
				.select()
				.from(patient)
				.where(eq(patient.ccId, criteria.ccId))
				.limit(1);
			return results[0] || null;
		}

		// Priority 2: Fallback search by email and phone
		if (criteria.email || criteria.phone) {
			const conditions = [];
			if (criteria.email) {
				conditions.push(eq(patient.email, criteria.email));
			}
			if (criteria.phone) {
				conditions.push(eq(patient.phone, criteria.phone));
			}

			if (conditions.length > 0) {
				const results = await db
					.select()
					.from(patient)
					.where(or(...conditions))
					.limit(1);
				return results[0] || null;
			}
		}

		return null;
	} catch (error) {
		await logError(
			"PATIENT_FIND_ERROR",
			error,
			{ criteria },
			"PatientStorage",
		);
		throw error;
	}
}
